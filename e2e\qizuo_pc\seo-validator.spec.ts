// @ts-check
import { test } from "./fixture";
import dotenv from "dotenv";
import { expect } from "@playwright/test";
import { SEOValidatorPage } from "./pages/seo-validator.page";
import { SEOTestData } from "./data/seo-test-data";
import fs from 'fs';
import path from 'path';

// 加载环境变量
dotenv.config();

// 定义基础URL
const BASE_URL = process.env.QIZUO_PC_URL || 'https://test-www.aitojoy.com';

// 定义各个子域名的URL配置
const URL_CONFIG = {
  // 主站
  main: BASE_URL,
  // 搜索
  search: process.env.QIZUO_SEARCH_URL || 'https://test-so.aitojoy.com',
  // 找项目
  project: process.env.QIZUO_PROJECT_URL || 'https://test-xiangmu.aitojoy.com',
  // 聚活动
  activity: process.env.QIZUO_ACTIVITY_URL || 'https://test-huodong.aitojoy.com',
  // 人脉广场
  connection: process.env.QIZUO_CONNECTION_URL || 'https://test-renmai.aitojoy.com',
  // 产业服务
  industryService: process.env.QIZUO_INDUSTRY_SERVICE_URL || 'https://test-chanyefuwu.aitojoy.com',
  // 惠企政策
  policy: process.env.QIZUO_POLICY_URL || 'https://test-zhengce.aitojoy.com',
  // 企业诊断
  diagnosis: process.env.QIZUO_DIAGNOSIS_URL || 'https://test-qiyezhenduan.aitojoy.com',
  // 联营服务
  jointOperation: process.env.QIZUO_JOINT_OPERATION_URL || 'https://test-lianyingfuwu.aitojoy.com',
  // 创业课堂
  course: process.env.QIZUO_COURSE_URL || 'https://test-ketang.aitojoy.com',
  // 产业资讯
  news: process.env.QIZUO_NEWS_URL || 'https://test-zixun.aitojoy.com',
  // 资金服务
  funding: process.env.QIZUO_FUNDING_URL || 'https://test-zijinfuwu.aitojoy.com',
  // 企业服务
  enterpriseService: process.env.QIZUO_ENTERPRISE_SERVICE_URL || 'https://test-qiyefuwu.aitojoy.com',
  // AI落地页
  ai: process.env.QIZUO_AI_URL || 'https://test-ai.aitojoy.com'
};

// 创建报告目录
const reportDir = path.join(process.cwd(), 'reports', 'seo-reports');
if (!fs.existsSync(reportDir)) {
  fs.mkdirSync(reportDir, { recursive: true });
}

test.describe('企座网站SEO验证测试', () => {
  // 在每个测试后保存SEO报告
  test.afterEach(async ({ page }, testInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const report = await seoValidator.generateSEOReport();

    const reportPath = path.join(reportDir, `${testInfo.title.replace(/\s+/g, '-')}.txt`);
    fs.writeFileSync(reportPath, report);
    console.log(`SEO报告已保存至: ${reportPath}`);
  });

  // 首页测试
  test('首页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.homePage;

    // 访问首页
    await page.goto(URL_CONFIG.main);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');


    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`首页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 添加断言，确保测试在SEO验证失败时会标记为失败
    expect(result.success, `首页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 全局搜索页测试
  test('全局搜索页SEO元素验证', async ({
    page,
    ai,
    aiTap
  }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.searchPage;
    const searchKeyword = "人工智能";

    // 定义所有搜索类型
    const searchTypes = [
      { name: '全站', tabText: '全站', titleSuffix: '' },
      { name: '项目', tabText: '项目', titleSuffix: '创新项目搜索' },
      { name: '活动', tabText: '活动', titleSuffix: '商机活动搜索' },
      { name: '企业', tabText: '企业', titleSuffix: '企业搜索' },
      { name: '机构', tabText: '机构', titleSuffix: '机构搜索' },
      { name: '人脉', tabText: '人脉', titleSuffix: '人脉搜索' },
      { name: '课程', tabText: '课程', titleSuffix: '课程搜索' },
      { name: '政策', tabText: '政策', titleSuffix: '政策搜索' }
    ];

    // 遍历所有搜索类型
    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    for (const searchType of searchTypes) {
      console.log(`测试搜索类型: ${searchType.name}`);

      // 访问搜索页
      await page.goto(URL_CONFIG.search);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 输入搜索关键词
      await ai(`在搜索框内输入"${searchKeyword}"并触发搜索`);

      // 等待搜索结果页面加载
      // await page.waitForURL(/.*so\.aitojoy\.com.*/);
      await page.waitForLoadState('networkidle');

      // 如果不是"全部"搜索，则点击对应的搜索类型标签
      if (searchType.name !== '全部') {
        await aiTap(`搜索条上面的搜索类型选项卡"${searchType.tabText}"`);
        await page.waitForLoadState('networkidle');
      }

      // 创建自定义测试数据，包含动态生成的标题和URL
      const expectedTitle = `${searchKeyword} - 企座${searchType.titleSuffix}`;
      const customTestData = {
        title: expectedTitle,
        keywords: testData.keywords,
        description: testData.description,
        url: `so.aitojoy.com/${searchKeyword}`
      };

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(customTestData);

      // 记录验证结果，但不中断测试
      console.log(`${searchType.name}搜索页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: searchType.name,
          errors: result.errors
        });
      }
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}搜索SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个搜索类型的SEO验证错误:\n${errorMessage}`).toBe(0);
    }

  });

  // 找项目-列表页测试
  // 1. 基本列表页测试
  test('找项目-基本列表页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试基本找项目列表页');
    const baseTestData = SEOTestData.projectListPage;

    // 访问找项目列表页
    await page.goto(URL_CONFIG.project);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(baseTestData);

    // 记录验证结果
    console.log(`基本找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 断言是否全部通过
    expect(result.success, `基本找项目列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 2. 单个行业筛选测试
  test('找项目-单个行业筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试单个行业筛选找项目列表页');

    // 访问找项目列表页
    await page.goto(URL_CONFIG.project);
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];
    const singleIndustries = ['农业', '建筑'];

    for (const industryName of singleIndustries) {
      console.log(`测试单个行业: ${industryName}`);
      const industryTestData = SEOTestData.projectListPageIndustryFilter(industryName);

      // 访问行业筛选页面
      await aiTap(`"${industryName}"行业分类选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(industryTestData);

      // 记录验证结果，但不中断测试
      console.log(`${industryName}行业筛选找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${industryName}行业筛选找项目列表页`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      await aiTap(`"${industryName}"行业分类选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个单个行业筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 3. 多行业组合筛选测试
  test('找项目-多行业组合筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试多行业组合筛选找项目列表页');

    // 访问找项目列表页
    await page.goto(URL_CONFIG.project);
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];
    const multiIndustries = [['农业', '医疗健康']];

    for (const industryGroup of multiIndustries) {
      console.log(`测试多行业组合: ${industryGroup.join(', ')}`);

      // 选择多个行业
      for (const industryName of industryGroup) {
        await aiTap(`"${industryName}"行业分类选项`);
        await page.waitForLoadState('networkidle');
      }

      // 生成测试数据
      const industryTestData = SEOTestData.projectListPageIndustryFilter(industryGroup);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(industryTestData);

      // 记录验证结果，但不中断测试
      console.log(`${industryGroup.join('+')}行业组合筛选找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${industryGroup.join('+')}行业组合筛选找项目列表页`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      for (const industryName of industryGroup) {
        await aiTap(`"${industryName}"行业分类选项`);
        await page.waitForLoadState('networkidle');
      }
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个多行业组合筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 4. 单级地区筛选测试
  test('找项目-单级地区筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试单级地区筛选找项目列表页');

    // 访问找项目列表页
    await page.goto(URL_CONFIG.project);
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];
    const singleRegions = ['浙江省'];

    for (const regionName of singleRegions) {
      console.log(`测试单级地区: ${regionName}`);
      const regionTestData = SEOTestData.projectListPageRegionFilter(regionName);

      // 访问地区筛选页面
      await aiTap(`"${regionName}"国家地区选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(regionTestData);

      // 记录验证结果，但不中断测试
      console.log(`${regionName}地区筛选找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${regionName}地区筛选找项目列表页`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      await aiTap(`"${regionName}"国家地区选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个单级地区筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 5. 省市二级地区筛选测试
  test('找项目-省市二级地区筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试省市二级地区筛选找项目列表页');

    // 访问找项目列表页
    await page.goto(URL_CONFIG.project);
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];
    const multiRegions = [['浙江省', '杭州市']];

    for (const regionGroup of multiRegions) {
      console.log(`测试省市二级地区: ${regionGroup.join('-')}`);

      // 选择省份和城市
      for (const regionName of regionGroup) {
        await aiTap(`"${regionName}"国家地区选项`);
        await page.waitForLoadState('networkidle');
      }

      // 生成测试数据
      const regionTestData = SEOTestData.projectListPageRegionFilter(regionGroup);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(regionTestData);

      // 记录验证结果，但不中断测试
      console.log(`${regionGroup.join('-')}省市二级地区筛选找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${regionGroup.join('-')}省市二级地区筛选找项目列表页`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      for (const regionName of regionGroup) {
        await aiTap(`"${regionName}"国家地区选项`);
        await page.waitForLoadState('networkidle');
      }
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个省市二级地区筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 6. 金额筛选测试
  test('找项目-金额筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试金额筛选找项目列表页');

    // 访问找项目列表页
    await page.goto(URL_CONFIG.project);
    await page.waitForLoadState('networkidle');

    const amountRange = '30万-50万';
    const amountTestData = SEOTestData.projectListPageAmountFilter(amountRange);

    // 访问金额筛选页面
    await aiTap(`"${amountRange}"合作金额选项`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(amountTestData);

    // 记录验证结果
    console.log(`金额筛选找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 断言是否全部通过
    expect(result.success, `金额筛选找项目列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 7. 组合筛选测试
  test('找项目-组合筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试组合筛选找项目列表页');

    // 访问找项目列表页
    await page.goto(URL_CONFIG.project);
    await page.waitForLoadState('networkidle');

    const selectedIndustry = '农业';
    const selectedRegion = '浙江省';
    const amountRange = '30万-50万';

    // 选择筛选条件
    await aiTap(`"${selectedIndustry}"行业分类选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${selectedRegion}"国家地区选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${amountRange}"合作金额选项`);
    await page.waitForLoadState('networkidle');

    // 生成测试数据
    const combinedTestData = SEOTestData.projectListPageCombinedFilter(selectedRegion, selectedIndustry, amountRange);

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(combinedTestData);

    // 记录验证结果
    console.log(`组合筛选找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 断言是否全部通过
    expect(result.success, `组合筛选找项目列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 8. 项目详情页测试
  test('找项目-详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试项目详情页');

    // 访问找项目列表页
    await page.goto(URL_CONFIG.project);
    await page.waitForLoadState('networkidle');

    // 等待项目列表加载完成
    await page.waitForSelector('.project-item', { timeout: 10000 });

    // 获取第一个项目的信息
    const projectElement = page.locator('.project-item').first();

    // 获取项目名称（假设项目名称在.project-title元素中）
    const projectName = await projectElement.locator('.project-title').textContent() || '示例项目';

    // 获取项目所属地区（假设在.project-region元素中）
    const projectRegion = await projectElement.locator('.project-region').textContent() || '浙江省';

    // 获取项目所属行业（假设在.project-industry元素中）
    const projectIndustry = await projectElement.locator('.project-industry').textContent() || '农业';

    // 获取项目合作金额（假设在.project-amount元素中）
    const projectAmount = await projectElement.locator('.project-amount').textContent() || '30万-50万';

    // 点击项目进入详情页
    await projectElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取项目ID
    const currentUrl = page.url();
    const projectId = currentUrl.split('/').pop() || 'project123';

    // 生成测试数据
    const detailTestData = SEOTestData.projectDetailPage(
      projectName,
      projectId,
      projectRegion,
      projectIndustry,
      projectAmount
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`项目详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 断言是否全部通过
    expect(result.success, `项目详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });



  // 人脉广场-频道页测试
  test('人脉广场-频道页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.connectionChannelPage;

    // 访问人脉广场频道页
    await page.goto(URL_CONFIG.connection);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`人脉广场频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 最后断言是否全部通过，但不会中断测试流程
    expect(result.success, `SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 人脉广场-列表页测试
  test('人脉广场-列表页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.connectionListPage;

    // 访问人脉广场列表页
    await page.goto(`${URL_CONFIG.connection}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`人脉广场列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `人脉广场列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 人脉广场-详情页测试
  test('人脉广场-详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试人脉广场详情页');

    // 访问人脉广场列表页
    await page.goto(`${URL_CONFIG.connection}/list`);
    await page.waitForLoadState('networkidle');

    // 等待人脉列表加载完成
    await page.waitForSelector('.person-item', { timeout: 10000 });

    // 获取第一个人脉的信息
    const personElement = page.locator('.person-item').first();

    // 获取人脉姓名（假设人脉姓名在.person-name元素中）
    const personName = await personElement.locator('.person-name').textContent() || '示例人脉';

    // 获取企业名称（假设在.company-name元素中）
    const companyName = await personElement.locator('.company-name').textContent() || '示例企业';

    // 点击人脉进入详情页
    await personElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取人脉ID
    const currentUrl = page.url();
    const personId = currentUrl.split('/').pop() || 'person123';

    // 生成测试数据
    const detailTestData = SEOTestData.connectionDetailPage(
      companyName,
      personName,
      personId
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`人脉广场详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${detailTestData.keywords.join(', ')}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `人脉广场详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 聚活动-频道页测试
  test('聚活动-频道页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.activityChannelPage;

    // 访问聚活动频道页
    await page.goto(URL_CONFIG.activity);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`聚活动频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 最后断言是否全部通过
    expect(result.success, `聚活动频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 聚活动-列表页测试
  test('聚活动-列表页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.activityListPage;

    // 访问聚活动列表页
    await page.goto(`${URL_CONFIG.activity}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`聚活动列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 最后断言是否全部通过
    expect(result.success, `聚活动列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 聚活动-列表页-举办地点筛选测试
  test('聚活动-列表页-举办地点筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问聚活动列表页
    await page.goto(`${URL_CONFIG.activity}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的举办地点及其拼音
    const locations = [
      { name: '北京', pinyin: 'beijing' },
      { name: '上海', pinyin: 'shanghai' }
    ];

    for (const location of locations) {
      console.log(`测试举办地点: ${location.name}`);

      // 点击举办地点筛选选项
      await aiTap(`"${location.name}"举办地点选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const locationTestData = SEOTestData.activityListPageLocationFilter(location.name, location.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(locationTestData);

      // 记录验证结果，但不中断测试
      console.log(`${location.name}举办地点筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${location.name}举办地点筛选`,
          errors: result.errors
        });
      }

      // 详细输出验证的TDK信息
      console.log(`验证的${location.name}举办地点筛选TDK信息:`);
      console.log(`标题: ${locationTestData.title}`);
      console.log(`关键词: ${locationTestData.keywords.join(', ')}`);
      console.log(`描述: ${locationTestData.description}`);
      console.log(`URL: ${locationTestData.url}`);

      // 清除筛选条件
      await aiTap(`"${location.name}"举办地点选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个举办地点筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 聚活动-详情页测试
  test('聚活动-详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试聚活动详情页');

    // 访问聚活动列表页
    await page.goto(`${URL_CONFIG.activity}/list`);
    await page.waitForLoadState('networkidle');

    // 等待活动列表加载完成
    await page.waitForSelector('.activity-item', { timeout: 10000 });

    // 获取第一个活动的信息
    const activityElement = page.locator('.activity-item').first();

    // 获取活动名称（假设活动名称在.activity-title元素中）
    const activityName = await activityElement.locator('.activity-title').textContent() || '示例活动';

    // 获取活动所在省份（假设在.activity-province元素中）
    const activityProvince = await activityElement.locator('.activity-province').textContent() || '浙江省';

    // 获取活动类型（假设在.activity-type元素中）
    const activityType = await activityElement.locator('.activity-type').textContent() || '项目路演';

    // 点击活动进入详情页
    await activityElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取活动ID
    const currentUrl = page.url();
    const activityId = currentUrl.split('/').pop() || 'activity123';

    // 生成测试数据
    const detailTestData = SEOTestData.activityDetailPage(
      activityName,
      activityId,
      activityProvince,
      activityType
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`聚活动详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${detailTestData.keywords.join(', ')}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `聚活动详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业服务-频道页测试
  test('产业服务-频道页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryServiceChannelPage;

    // 访问产业服务频道页
    await page.goto(URL_CONFIG.industryService);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`产业服务频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 最后断言是否全部通过
    expect(result.success, `产业服务频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业服务-列表页测试
  test('产业服务-列表页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryServiceListPage;

    // 访问产业服务列表页
    await page.goto(`${URL_CONFIG.industryService}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`产业服务列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 最后断言是否全部通过
    expect(result.success, `产业服务列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业服务-列表页-所在地筛选测试
  test('产业服务-列表页-所在地筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问产业服务列表页
    await page.goto(`${URL_CONFIG.industryService}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的地区
    const regions = ['北京', '上海', '广东'];

    for (const region of regions) {
      console.log(`测试所在地: ${region}`);

      // 点击所在地筛选选项
      await aiTap(`"${region}"所在地选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const regionTestData = SEOTestData.industryServiceListPageRegionFilter(region);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(regionTestData);

      // 记录验证结果，但不中断测试
      console.log(`${region}所在地筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${region}所在地筛选`,
          errors: result.errors
        });
      }

      // 详细输出验证的TDK信息
      console.log(`验证的${region}所在地筛选TDK信息:`);
      console.log(`标题: ${regionTestData.title}`);
      console.log(`关键词: ${regionTestData.keywords.join(', ')}`);
      console.log(`描述: ${regionTestData.description}`);
      console.log(`URL: ${regionTestData.url}`);

      // 清除筛选条件
      await aiTap(`"${region}"所在地选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个所在地筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 产业服务-详情页测试
  test('产业服务-详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试产业服务详情页');

    // 访问产业服务列表页
    await page.goto(`${URL_CONFIG.industryService}/list`);
    await page.waitForLoadState('networkidle');

    // 等待企业列表加载完成
    await page.waitForSelector('.company-item', { timeout: 10000 });

    // 获取第一个企业的信息
    const companyElement = page.locator('.company-item').first();

    // 获取企业名称（假设企业名称在.company-name元素中）
    const companyName = await companyElement.locator('.company-name').textContent() || '示例企业';

    // 点击企业进入详情页
    await companyElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取企业ID
    const currentUrl = page.url();
    const companyId = currentUrl.split('/').pop() || 'company123';

    // 生成测试数据
    const detailTestData = SEOTestData.industryServiceDetailPage(companyName, companyId);

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`产业服务详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${detailTestData.keywords.join(', ')}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业服务详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 惠企政策-频道页测试
  test('惠企政策-频道页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.policyChannelPage;

    // 访问惠企政策频道页
    await page.goto(URL_CONFIG.policy);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');


    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`惠企政策频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 最后断言是否全部通过，但不会中断测试流程
    expect(result.success, `SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 惠企政策-列表页-奖补政策tab测试
  test('惠企政策-列表页-奖补政策tabSEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.policyRewardListPage;

    // 访问惠企政策奖补政策列表页
    await page.goto(`${URL_CONFIG.policy}/jiangbu`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`惠企政策-列表页-奖补政策tabSEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 断言是否全部通过
    expect(result.success, `惠企政策-列表页-奖补政策tabSEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 惠企政策-列表页-奖补政策tab-发布地区筛选测试
  test('惠企政策-列表页-奖补政策tab-发布地区筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问惠企政策奖补政策列表页
    await page.goto(`${URL_CONFIG.policy}/jiangbu`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的发布地区及其拼音
    const regions = [
      { name: '北京', pinyin: 'beijing' },
      { name: '上海', pinyin: 'shanghai' }
    ];

    for (const region of regions) {
      console.log(`测试发布地区: ${region.name}`);

      // 点击发布地区筛选选项
      await aiTap(`"${region.name}"发布地区选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const regionTestData = SEOTestData.policyRewardListPageRegionFilter(region.name, region.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(regionTestData);

      // 记录验证结果，但不中断测试
      console.log(`${region.name}发布地区筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${region.name}发布地区筛选`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      await aiTap(`"${region.name}"发布地区选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个发布地区筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 惠企政策-列表页-奖补政策tab-政策类型筛选测试
  test('惠企政策-列表页-奖补政策tab-政策类型筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问惠企政策奖补政策列表页
    await page.goto(`${URL_CONFIG.policy}/jiangbu`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的政策类型及其拼音
    const policyTypes = [
      { name: '人才补贴', pinyin: 'rencaibutie' },
      { name: '研发补贴', pinyin: 'yanfabutie' }
    ];

    for (const policyType of policyTypes) {
      console.log(`测试政策类型: ${policyType.name}`);

      // 点击政策类型筛选选项
      await aiTap(`"${policyType.name}"政策类型选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const typeTestData = SEOTestData.policyRewardListPageTypeFilter(policyType.name, policyType.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(typeTestData);

      // 记录验证结果，但不中断测试
      console.log(`${policyType.name}政策类型筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${policyType.name}政策类型筛选`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      await aiTap(`"${policyType.name}"政策类型选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个政策类型筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 惠企政策-列表页-奖补政策tab-组合筛选测试
  test('惠企政策-列表页-奖补政策tab-组合筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问惠企政策奖补政策列表页
    await page.goto(`${URL_CONFIG.policy}/jiangbu`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 定义组合筛选条件
    const region = '北京';
    const policyType = '人才补贴';
    const regionPinyin = 'beijing';
    const policyTypePinyin = 'rencaibutie';

    console.log(`测试组合筛选: ${region} + ${policyType}`);

    // 点击筛选选项
    await aiTap(`"${region}"发布地区选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${policyType}"政策类型选项`);
    await page.waitForLoadState('networkidle');

    // 生成测试数据
    const combinedTestData = SEOTestData.policyRewardListPageCombinedFilter(
      region,
      policyType,
      regionPinyin,
      policyTypePinyin
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(combinedTestData);

    // 记录验证结果
    console.log(`组合筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 清除筛选条件
    await aiTap(`"${region}"发布地区选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${policyType}"政策类型选项`);
    await page.waitForLoadState('networkidle');

    // 断言是否全部通过
    expect(result.success, `组合筛选SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 惠企政策-列表页-热点政策tab测试
  test('惠企政策-列表页-热点政策tabSEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.policyHotListPage;

    // 访问惠企政策热点政策列表页
    await page.goto(`${URL_CONFIG.policy}/redian`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`惠企政策-列表页-热点政策tabSEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 断言是否全部通过
    expect(result.success, `惠企政策-列表页-热点政策tabSEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 惠企政策-列表页-热点政策tab-发布地区筛选测试
  test('惠企政策-列表页-热点政策tab-发布地区筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问惠企政策热点政策列表页
    await page.goto(`${URL_CONFIG.policy}/redian`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的发布地区及其拼音
    const regions = [
      { name: '北京', pinyin: 'beijing' },
      { name: '上海', pinyin: 'shanghai' }
    ];

    for (const region of regions) {
      console.log(`测试发布地区: ${region.name}`);

      // 点击发布地区筛选选项
      await aiTap(`"${region.name}"发布地区选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const regionTestData = SEOTestData.policyHotListPageRegionFilter(region.name, region.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(regionTestData);

      // 记录验证结果，但不中断测试
      console.log(`${region.name}发布地区筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${region.name}发布地区筛选`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      await aiTap(`"${region.name}"发布地区选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个发布地区筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 惠企政策-列表页-热点政策tab-适用行业筛选测试
  test('惠企政策-列表页-热点政策tab-适用行业筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问惠企政策热点政策列表页
    await page.goto(`${URL_CONFIG.policy}/redian`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的适用行业及其拼音
    const industries = [
      { name: '人工智能', pinyin: 'rengongzhineng' },
      { name: '新能源', pinyin: 'xinnengyuan' }
    ];

    for (const industry of industries) {
      console.log(`测试适用行业: ${industry.name}`);

      // 点击适用行业筛选选项
      await aiTap(`"${industry.name}"适用行业选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const industryTestData = SEOTestData.policyHotListPageIndustryFilter(industry.name, industry.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(industryTestData);

      // 记录验证结果，但不中断测试
      console.log(`${industry.name}适用行业筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${industry.name}适用行业筛选`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      await aiTap(`"${industry.name}"适用行业选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个适用行业筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 惠企政策-列表页-热点政策tab-政策类型筛选测试
  test('惠企政策-列表页-热点政策tab-政策类型筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问惠企政策热点政策列表页
    await page.goto(`${URL_CONFIG.policy}/redian`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的政策类型及其拼音
    const policyTypes = [
      { name: '人才补贴', pinyin: 'rencaibutie' },
      { name: '研发补贴', pinyin: 'yanfabutie' }
    ];

    for (const policyType of policyTypes) {
      console.log(`测试政策类型: ${policyType.name}`);

      // 点击政策类型筛选选项
      await aiTap(`"${policyType.name}"政策类型选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const typeTestData = SEOTestData.policyHotListPageTypeFilter(policyType.name, policyType.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(typeTestData);

      // 记录验证结果，但不中断测试
      console.log(`${policyType.name}政策类型筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${policyType.name}政策类型筛选`,
          errors: result.errors
        });
      }

      // 清除筛选条件
      await aiTap(`"${policyType.name}"政策类型选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个政策类型筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 惠企政策-列表页-热点政策tab-组合筛选测试
  test('惠企政策-列表页-热点政策tab-组合筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问惠企政策热点政策列表页
    await page.goto(`${URL_CONFIG.policy}/redian`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 定义组合筛选条件
    const region = '北京';
    const industry = '人工智能';
    const policyType = '人才补贴';
    const regionPinyin = 'beijing';
    const industryPinyin = 'rengongzhineng';
    const policyTypePinyin = 'rencaibutie';

    console.log(`测试组合筛选: ${region} + ${industry} + ${policyType}`);

    // 点击筛选选项
    await aiTap(`"${region}"发布地区选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${industry}"适用行业选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${policyType}"政策类型选项`);
    await page.waitForLoadState('networkidle');

    // 生成测试数据
    const combinedTestData = SEOTestData.policyHotListPageCombinedFilter(
      region,
      industry,
      policyType,
      regionPinyin,
      industryPinyin,
      policyTypePinyin
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(combinedTestData);

    // 记录验证结果
    console.log(`组合筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 清除筛选条件
    await aiTap(`"${region}"发布地区选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${industry}"适用行业选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${policyType}"政策类型选项`);
    await page.waitForLoadState('networkidle');

    // 断言是否全部通过
    expect(result.success, `组合筛选SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 惠企政策-详情页-奖补政策测试
  test('惠企政策-详情页-奖补政策SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试惠企政策-详情页-奖补政策');

    // 访问惠企政策奖补政策列表页
    await page.goto(`${URL_CONFIG.policy}/jiangbu`);
    await page.waitForLoadState('networkidle');

    // 等待政策列表加载完成
    await page.waitForSelector('.policy-item', { timeout: 10000 });

    // 获取第一个政策的信息
    const policyElement = page.locator('.policy-item').first();

    // 获取政策名称（假设政策名称在.policy-title元素中）
    const policyName = await policyElement.locator('.policy-title').textContent() || '示例政策';

    // 点击政策进入详情页
    await policyElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取政策ID
    const currentUrl = page.url();
    const policyId = currentUrl.split('/').pop() || 'policy123';

    // 生成测试数据
    const detailTestData = SEOTestData.policyRewardDetailPage(policyName, policyId);

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`惠企政策-详情页-奖补政策SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 断言是否全部通过
    expect(result.success, `惠企政策-详情页-奖补政策SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 惠企政策-详情页-热点政策测试
  test('惠企政策-详情页-热点政策SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试惠企政策-详情页-热点政策');

    // 访问惠企政策热点政策列表页
    await page.goto(`${URL_CONFIG.policy}/redian`);
    await page.waitForLoadState('networkidle');

    // 等待政策列表加载完成
    await page.waitForSelector('.policy-item', { timeout: 10000 });

    // 获取第一个政策的信息
    const policyElement = page.locator('.policy-item').first();

    // 获取政策名称（假设政策名称在.policy-title元素中）
    const policyName = await policyElement.locator('.policy-title').textContent() || '示例政策';

    // 点击政策进入详情页
    await policyElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取政策ID
    const currentUrl = page.url();
    const policyId = currentUrl.split('/').pop() || 'policy123';

    // 生成测试数据
    const detailTestData = SEOTestData.policyHotDetailPage(policyName, policyId);

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`惠企政策-详情页-热点政策SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 断言是否全部通过
    expect(result.success, `惠企政策-详情页-热点政策SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 企业诊断页测试
  test('企业诊断页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.enterpriseDiagnosisPage;

    // 访问企业诊断页
    await page.goto(URL_CONFIG.diagnosis);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');


    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`企业诊断页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 最后断言是否全部通过，但不会中断测试流程
    expect(result.success, `SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 联营服务页测试
  test('联营服务页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.jointOperationServicePage;

    // 访问联营服务页
    await page.goto(URL_CONFIG.jointOperation);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');


    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`联营服务页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 最后断言是否全部通过，但不会中断测试流程
    expect(result.success, `SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
  // 创业课堂-频道页测试
  test('创业课堂-频道页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.courseChannelPage;

    // 访问创业课堂频道页
    await page.goto(URL_CONFIG.course);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`创业课堂频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 最后断言是否全部通过，但不会中断测试流程
    expect(result.success, `SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
  // 创业课堂-列表页测试
  test('创业课堂-列表页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.courseListPage;

    // 访问创业课堂列表页
    await page.goto(`${URL_CONFIG.course}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果，但不中断测试
    console.log(`创业课堂列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 最后断言是否全部通过
    expect(result.success, `创业课堂列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 创业课堂-列表页-课程分类筛选测试
  test('创业课堂-列表页-课程分类筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问创业课堂列表页
    await page.goto(`${URL_CONFIG.course}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的课程分类及其拼音
    const categories = [
      { name: '管理能力', pinyin: 'guanlinengliketang' },
      { name: '专业技能', pinyin: 'zhuanyejinengketang' }
    ];

    for (const category of categories) {
      console.log(`测试课程分类: ${category.name}`);

      // 点击课程分类筛选选项
      await aiTap(`"${category.name}"课程分类选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const categoryTestData = SEOTestData.courseListPageCategoryFilter(category.name, category.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(categoryTestData);

      // 记录验证结果，但不中断测试
      console.log(`${category.name}课程分类筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${category.name}课程分类筛选`,
          errors: result.errors
        });
      }

      // 详细输出验证的TDK信息
      console.log(`验证的${category.name}课程分类筛选TDK信息:`);
      console.log(`标题: ${categoryTestData.title}`);
      console.log(`关键词: ${categoryTestData.keywords.join(', ')}`);
      console.log(`描述: ${categoryTestData.description}`);
      console.log(`URL: ${categoryTestData.url}`);

      // 清除筛选条件
      await aiTap(`"${category.name}"课程分类选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个课程分类筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 创业课堂-列表页-课程标签筛选测试
  test('创业课堂-列表页-课程标签筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问创业课堂列表页
    await page.goto(`${URL_CONFIG.course}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的课程标签及其拼音
    const tags = [
      { name: '热门课程', pinyin: 'remenketang' },
      { name: '新手必学', pinyin: 'xinshubixue' }
    ];

    for (const tag of tags) {
      console.log(`测试课程标签: ${tag.name}`);

      // 点击课程标签筛选选项
      await aiTap(`"${tag.name}"课程标签选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const tagTestData = SEOTestData.courseListPageTagFilter(tag.name, tag.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(tagTestData);

      // 记录验证结果，但不中断测试
      console.log(`${tag.name}课程标签筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${tag.name}课程标签筛选`,
          errors: result.errors
        });
      }

      // 详细输出验证的TDK信息
      console.log(`验证的${tag.name}课程标签筛选TDK信息:`);
      console.log(`标题: ${tagTestData.title}`);
      console.log(`关键词: ${tagTestData.keywords.join(', ')}`);
      console.log(`描述: ${tagTestData.description}`);
      console.log(`URL: ${tagTestData.url}`);

      // 清除筛选条件
      await aiTap(`"${tag.name}"课程标签选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个课程标签筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 创业课堂-列表页-适合岗位筛选测试
  test('创业课堂-列表页-适合岗位筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问创业课堂列表页
    await page.goto(`${URL_CONFIG.course}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 创建一个数组来收集所有的错误信息
    const allErrors: {type: string, errors: string[]}[] = [];

    // 定义要测试的适合岗位及其拼音
    const positions = [
      { name: '管理层', pinyin: 'guanliceng' },
      { name: '销售', pinyin: 'xiaoshou' }
    ];

    for (const position of positions) {
      console.log(`测试适合岗位: ${position.name}`);

      // 点击适合岗位筛选选项
      await aiTap(`"${position.name}"适合岗位选项`);

      // 等待页面加载完成
      await page.waitForLoadState('networkidle');

      // 生成测试数据
      const positionTestData = SEOTestData.courseListPagePositionFilter(position.name, position.pinyin);

      // 使用软断言验证所有SEO元素
      const result = await seoValidator.validateTDKWithSoftAssertions(positionTestData);

      // 记录验证结果，但不中断测试
      console.log(`${position.name}适合岗位筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        // 收集错误信息，但不立即断言
        allErrors.push({
          type: `${position.name}适合岗位筛选`,
          errors: result.errors
        });
      }

      // 详细输出验证的TDK信息
      console.log(`验证的${position.name}适合岗位筛选TDK信息:`);
      console.log(`标题: ${positionTestData.title}`);
      console.log(`关键词: ${positionTestData.keywords.join(', ')}`);
      console.log(`描述: ${positionTestData.description}`);
      console.log(`URL: ${positionTestData.url}`);

      // 清除筛选条件
      await aiTap(`"${position.name}"适合岗位选项`);
      await page.waitForLoadState('networkidle');
    }

    // 循环结束后，检查是否有收集到的错误
    if (allErrors.length > 0) {
      // 格式化所有错误信息
      const errorMessage = allErrors.map(err =>
        `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
      ).join('\n\n');

      // 一次性断言所有错误
      expect(allErrors.length, `发现多个适合岗位筛选SEO验证错误:\n${errorMessage}`).toBe(0);
    }
  });

  // 创业课堂-列表页-组合筛选测试
  test('创业课堂-列表页-组合筛选SEO元素验证', async ({ page, aiTap }) => {
    const seoValidator = new SEOValidatorPage(page);

    // 访问创业课堂列表页
    await page.goto(`${URL_CONFIG.course}/list`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 定义组合筛选条件
    const category = '管理能力';
    const tag = '热门课程';
    const position = '管理层';
    const categoryPinyin = 'guanlinengliketang';
    const tagPinyin = 'remenketang';
    const positionPinyin = 'guanliceng';

    console.log(`测试组合筛选: ${category} + ${tag} + ${position}`);

    // 点击筛选选项
    await aiTap(`"${category}"课程分类选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${tag}"课程标签选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${position}"适合岗位选项`);
    await page.waitForLoadState('networkidle');

    // 生成测试数据
    const combinedTestData = SEOTestData.courseListPageCombinedFilter(
      category,
      tag,
      position,
      categoryPinyin,
      tagPinyin,
      positionPinyin
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(combinedTestData);

    // 记录验证结果
    console.log(`组合筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的组合筛选TDK信息:');
    console.log(`标题: ${combinedTestData.title}`);
    console.log(`关键词: ${combinedTestData.keywords.join(', ')}`);
    console.log(`描述: ${combinedTestData.description}`);
    console.log(`URL: ${combinedTestData.url}`);

    // 清除筛选条件
    await aiTap(`"${category}"课程分类选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${tag}"课程标签选项`);
    await page.waitForLoadState('networkidle');

    await aiTap(`"${position}"适合岗位选项`);
    await page.waitForLoadState('networkidle');

    // 断言是否全部通过
    expect(result.success, `组合筛选SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 创业课堂-详情页测试
  test('创业课堂-详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试创业课堂详情页');

    // 访问创业课堂列表页
    await page.goto(`${URL_CONFIG.course}/list`);
    await page.waitForLoadState('networkidle');

    // 等待课程列表加载完成
    await page.waitForSelector('.course-item', { timeout: 10000 });

    // 获取第一个课程的信息
    const courseElement = page.locator('.course-item').first();

    // 获取课程名称（假设课程名称在.course-title元素中）
    const courseName = await courseElement.locator('.course-title').textContent() || '示例课程';

    // 获取课程适合岗位（假设在.course-position元素中）
    const coursePosition = await courseElement.locator('.course-position').textContent() || '管理层';

    // 获取课程分类（假设在.course-category元素中）
    const courseCategory = await courseElement.locator('.course-category').textContent() || '管理能力';

    // 获取课程标签（假设在.course-tag元素中）
    const courseTag = await courseElement.locator('.course-tag').textContent() || '热门课程';

    // 获取课程简介（假设在.course-intro元素中）
    const courseIntro = await courseElement.locator('.course-intro').textContent() || '这是一门优秀的课程';

    // 点击课程进入详情页
    await courseElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取课程ID
    const currentUrl = page.url();
    const courseId = currentUrl.split('/').pop() || 'course123';

    // 生成测试数据
    const detailTestData = SEOTestData.courseDetailPage(
      courseName,
      courseId,
      coursePosition,
      courseCategory,
      courseTag,
      courseIntro
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`创业课堂详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${detailTestData.keywords.join(', ')}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `创业课堂详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 创业课堂-成长班列表页测试
  test('创业课堂-成长班列表页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.courseGrowthClassListPage;

    // 访问创业课堂成长班列表页
    await page.goto(`${URL_CONFIG.course}/chengzhangban`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`创业课堂成长班列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `创业课堂成长班列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 创业课堂-成长班详情页测试
  test('创业课堂-成长班详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试创业课堂成长班详情页');

    // 访问创业课堂成长班列表页
    await page.goto('https://test-ketang.aitojoy.com/chengzhangban');
    await page.waitForLoadState('networkidle');

    // 等待成长班列表加载完成
    await page.waitForSelector('.growth-class-item', { timeout: 10000 });

    // 获取第一个成长班的信息
    const growthClassElement = page.locator('.growth-class-item').first();

    // 获取成长班名称（假设成长班名称在.growth-class-title元素中）
    const growthClassName = await growthClassElement.locator('.growth-class-title').textContent() || '示例成长班';

    // 点击成长班进入详情页
    await growthClassElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取成长班ID
    const currentUrl = page.url();
    const growthClassId = currentUrl.split('/').pop() || 'growthclass123';

    // 生成测试数据
    const detailTestData = SEOTestData.courseGrowthClassDetailPage(
      growthClassName,
      growthClassId
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`创业课堂成长班详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${detailTestData.keywords.join(', ')}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `创业课堂成长班详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-频道页测试
  test('产业资讯-频道页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNewsChannelPage;

    // 访问产业资讯频道页
    await page.goto('https://test-zixun.aitojoy.com');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`产业资讯频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-列表页-7*24测试
  test('产业资讯-列表页-7*24SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNews24HListPage;

    // 访问产业资讯7*24列表页
    await page.goto('https://test-zixun.aitojoy.com/kuaixun');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`产业资讯-列表页-7*24SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯-列表页-7*24SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-列表页-市场聚焦测试
  test('产业资讯-列表页-市场聚焦SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNewsFocusListPage;

    // 访问产业资讯市场聚焦列表页
    await page.goto('https://test-zixun.aitojoy.com/jujiao');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`产业资讯-列表页-市场聚焦SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯-列表页-市场聚焦SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-列表页-市场洞察测试
  test('产业资讯-列表页-市场洞察SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNewsInsightListPage;

    // 访问产业资讯市场洞察列表页
    await page.goto('https://test-zixun.aitojoy.com/dongcha');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`产业资讯-列表页-市场洞察SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯-列表页-市场洞察SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-列表页-商讯快报测试
  test('产业资讯-列表页-商讯快报SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNewsBusinessListPage;

    // 访问产业资讯商讯快报列表页
    await page.goto('https://test-zixun.aitojoy.com/shangxunkuaibao');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`产业资讯-列表页-商讯快报SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯-列表页-商讯快报SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-详情页测试
  test('产业资讯-详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试产业资讯详情页');

    // 访问产业资讯7*24列表页
    await page.goto('https://test-zixun.aitojoy.com/kuaixun');
    await page.waitForLoadState('networkidle');

    // 等待资讯列表加载完成
    await page.waitForSelector('.news-item', { timeout: 10000 });

    // 获取第一个资讯的信息
    const newsElement = page.locator('.news-item').first();

    // 获取资讯标题（假设资讯标题在.news-title元素中）
    const newsTitle = await newsElement.locator('.news-title').textContent() || '示例资讯标题';

    // 获取资讯频道名称（假设在.news-channel元素中）
    const channelName = await newsElement.locator('.news-channel').textContent() || '7*24小时快讯';

    // 点击资讯进入详情页
    await newsElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取资讯ID
    const currentUrl = page.url();
    const newsId = currentUrl.split('/').pop() || 'news123';

    // 生成测试数据
    const detailTestData = SEOTestData.industryNewsDetailPage(
      channelName,
      newsTitle,
      newsId
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`产业资讯详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${detailTestData.keywords.join(', ')}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 资金服务-频道页测试
  test('资金服务-频道页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.fundingServiceChannelPage;

    // 访问资金服务频道页
    await page.goto('https://test-zijinfuwu.aitojoy.com');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`资金服务频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `资金服务频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 资金服务-机构列表页测试
  test('资金服务-机构列表页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.fundingServiceListPage;

    // 访问资金服务机构列表页
    await page.goto('https://test-zijinfuwu.aitojoy.com/list');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`资金服务机构列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `资金服务机构列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 资金服务-详情页测试
  test('资金服务-详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试资金服务详情页');

    // 访问资金服务机构列表页
    await page.goto('https://test-zijinfuwu.aitojoy.com/list');
    await page.waitForLoadState('networkidle');

    // 等待机构列表加载完成
    await page.waitForSelector('.institution-item', { timeout: 10000 });

    // 获取第一个机构的信息
    const institutionElement = page.locator('.institution-item').first();

    // 获取机构名称（假设机构名称在.institution-name元素中）
    const institutionName = await institutionElement.locator('.institution-name').textContent() || '示例投资机构';

    // 获取机构简介（假设在.institution-intro元素中）
    const institutionIntro = await institutionElement.locator('.institution-intro').textContent() || '';

    // 点击机构进入详情页
    await institutionElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取机构ID
    const currentUrl = page.url();
    const institutionId = currentUrl.split('/').pop() || 'institution123';

    // 生成测试数据
    const detailTestData = SEOTestData.fundingServiceDetailPage(
      institutionName,
      institutionId,
      institutionIntro
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`资金服务详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${detailTestData.keywords.join(', ')}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `资金服务详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 企业服务-频道页测试
  test('企业服务-频道页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.enterpriseServiceChannelPage;

    // 访问企业服务频道页
    await page.goto('https://test-qiyefuwu.aitojoy.com');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`企业服务频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `企业服务频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 企业服务-列表页测试
  test('企业服务-列表页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.enterpriseServiceListPage;

    // 访问企业服务列表页
    await page.goto('https://test-qiyefuwu.aitojoy.com/list');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`企业服务列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `企业服务列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 企业服务-详情页测试
  test('企业服务-详情页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试企业服务详情页');

    // 访问企业服务列表页
    await page.goto('https://test-qiyefuwu.aitojoy.com/list');
    await page.waitForLoadState('networkidle');

    // 等待服务列表加载完成
    await page.waitForSelector('.service-item', { timeout: 10000 });

    // 获取第一个服务的信息
    const serviceElement = page.locator('.service-item').first();

    // 获取服务分类（假设服务分类在.service-category元素中）
    const serviceCategory = await serviceElement.locator('.service-category').textContent() || '工商财税';

    // 获取服务名称（假设服务名称在.service-name元素中）
    const serviceName = await serviceElement.locator('.service-name').textContent() || '示例企业服务';

    // 获取服务优势介绍（假设在.service-intro元素中）
    const serviceIntro = await serviceElement.locator('.service-intro').textContent() || '';

    // 点击服务进入详情页
    await serviceElement.click();

    // 等待详情页加载完成
    await page.waitForLoadState('networkidle');

    // 获取当前URL，从中提取服务ID
    const currentUrl = page.url();
    const serviceId = currentUrl.split('/').pop() || 'service123';

    // 生成测试数据
    const detailTestData = SEOTestData.enterpriseServiceDetailPage(
      serviceCategory,
      serviceName,
      serviceId,
      serviceIntro
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);

    // 记录验证结果
    console.log(`企业服务详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${detailTestData.keywords.join(', ')}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `企业服务详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // AI落地页测试
  test('AI落地页SEO元素验证', async ({ page }) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.aiLandingPage;

    // 访问AI落地页
    await page.goto('https://test-ai.aitojoy.com');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions(testData);

    // 记录验证结果
    console.log(`AI落地页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${testData.keywords.join(', ')}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `AI落地页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
});




